# playwright-mind

基于 Playwright 和 midscene.js 的 AI 自动化测试项目，为 Web UI 测试注入 AI 智能能力。

---

## 技术栈

- [Playwright](https://github.com/microsoft/playwright)：Web UI 自动化测试工具
- [midscene.js](https://github.com/web-infra-dev/midscene)：AI 定位与断言能力

---

## 安装与配置

1. 克隆项目
   ```shell
   git clone https://github.com/autotestclass/playwright-mind
   ```
2. 安装依赖
   ```shell
   npm install --registry=https://registry.npmmirror.com
   ```
3. 安装浏览器
   ```shell
   npx playwright install
   ```
4. 配置环境变量（`.env` 文件示例）：
   ```env
   OPENAI_API_KEY=sk-your-key
   OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
   MIDSCENE_MODEL_NAME=qwen-vl-max-latest
   ```

---

## 目录结构简述

```
tests/
├── data/         # 测试数据配置（test-data.ts, urls.ts）
├── fixture/      # Playwright 测试夹具与断言
├── pages/        # 页面对象模型（POM）
├── utils/        # 通用工具类
├── laoyaoba/     # 具体业务测试用例
```

---

## 设计模式说明

- **页面对象模型（POM）**：每个页面有独立类，封装页面操作，提升可维护性与复用性。
- **测试数据分离**：所有测试数据、URL、AI指令等均集中管理，便于维护。
- **工具类**：提供常用辅助方法，如时间、字符串处理、兼容性补丁等。

---

## 示例用例

```typescript
import { test } from '../fixture/fixture';
import { LaoyaobaHomePage, OpinionPage } from '../pages';

test('获取最新舆情', async ({ page, ai, aiQuery, assert }) => {
  const homePage = new LaoyaobaHomePage(page, assert, ai, aiQuery);
  const opinionPage = new OpinionPage(page, assert, ai, aiQuery);

  await homePage.navigateToOpinion();
  await opinionPage.navigateToHotNews();

  const time = await opinionPage.getFirstNewsTime();
  assert.isTruthy(time, '时间格式应有效');
});
```

---

## 其他

- 支持 Windows 路径兼容与缓存管理
- 钉钉通知集成
- 推荐使用 AI 驱动的断言与交互


> 基于`playwright`和`midscene.js`自动化测试项目，给`Playwright`插上AI的翅膀，目前可以落地的AI自动化测试项目。

__技术栈：__

* [plywright](https://github.com/microsoft/playwright) Web UI自动化测试工具。

* [midscene.js](https://github.com/web-infra-dev/midscene) 提供AI定位断言能力。


## 安装与配置

1. 克隆项目到本地：

```shell
git clone https://github.com/autotestclass/playwright-mind
```

2. 安装依赖

```shell
npm install --registry=https://registry.npmmirror.com
```

3. 安装运行浏览器

```shell
npx playwright install
```

4. 配置大模型

> 本项目默认使用 `qwen-vl-max-latest` 模型, 经过验证可用，关键是免费。如果想其他模型请参考midscenejs官方配置。

阿里云百练：https://bailian.console.aliyun.com/

使用其他模型：https://midscenejs.com/zh/model-provider.html

在 `.env` 文件中配置环境变量：

```ts
export OPENAI_API_KEY="sk-your-key"
export OPENAI_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"
export MIDSCENE_MODEL_NAME="qwen-vl-max-latest"
```

三种关键方法：交互（.ai, .aiAction）, 提取 (.aiQuery), 断言 (.aiAssert)。

* `.ai`方法描述步骤并执行交互
* `.aiQuery` 从 UI 中“理解”并提取数据，返回值是 JSON 格式，你可以尽情描述想要的数据结构
* `.aiAssert` 来执行断言

__运行测试__

```shell
# 基本运行方式
npx playwright test --headed tests/bing-search-ai-example.spec.ts

# Windows 系统推荐使用（解决文件名兼容性问题）
npm run test:laoyaoba
