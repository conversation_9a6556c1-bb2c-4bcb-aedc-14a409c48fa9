/**
 * Windows 兼容性工具函数
 * 提供跨平台文件名处理和路径操作
 */

const fs = require('fs');
const path = require('path');

/**
 * 清理文件名中的特殊字符
 * @param {string} fileName - 原始文件名
 * @returns {string} 清理后的文件名
 */
function sanitizeFileName(fileName) {
  if (!fileName) return '';
  return String(fileName)
    .replace(/[<>:"\/\\|?*]/g, '-')  // 替换Windows不支持的字符
    .replace(/[（）()]/g, '-')      // 替换中文和英文括号
    .replace(/\s+/g, '-')          // 替换空格为连字符
    .replace(/-+/g, '-')           // 合并多个连字符
    .replace(/^-|-$/g, '');        // 去除开头和结尾的连字符
}

/**
 * 递归清理目录中的文件名
 * @param {string} dirPath - 目录路径
 * @returns {number} 处理的文件/目录数量
 */
function cleanDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`目录不存在: ${dirPath}`);
    return 0;
  }

  try {
    const items = fs.readdirSync(dirPath);
    let cleanedCount = 0;

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        // 递归处理子目录
        cleanedCount += cleanDirectory(itemPath);
        
        // 检查并清理目录名
        const newDirName = sanitizeFileName(item);
        if (newDirName !== item) {
          const newDirPath = path.join(dirPath, newDirName);
          try {
            fs.renameSync(itemPath, newDirPath);
            console.log(`重命名目录: ${item} -> ${newDirName}`);
            cleanedCount++;
          } catch (error) {
            console.warn(`无法重命名目录 ${item}:`, error.message);
          }
        }
      } else {
        // 处理文件名
        const newFileName = sanitizeFileName(item);
        if (newFileName !== item) {
          const newFilePath = path.join(dirPath, newFileName);
          try {
            fs.renameSync(itemPath, newFilePath);
            console.log(`重命名文件: ${item} -> ${newFileName}`);
            cleanedCount++;
          } catch (error) {
            console.warn(`无法重命名文件 ${item}:`, error.message);
          }
        }
      }
    }

    return cleanedCount;
  } catch (error) {
    console.warn(`清理目录 ${dirPath} 时出错:`, error.message);
    return 0;
  }
}

/**
 * 删除整个目录
 * @param {string} dirPath - 要删除的目录路径
 */
function removeDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`目录不存在: ${dirPath}`);
    return;
  }

  try {
    fs.rmSync(dirPath, { recursive: true, force: true });
    console.log(`已删除目录: ${dirPath}`);
  } catch (error) {
    console.warn(`删除目录 ${dirPath} 时出错:`, error.message);
  }
}

module.exports = {
  sanitizeFileName,
  cleanDirectory,
  removeDirectory
};
