#!/usr/bin/env node

/**
 * 清理 Midscene 缓存文件脚本
 * 处理 Windows 文件名兼容性问题
 */

const { cleanDirectory, removeDirectory } = require('./utils/windows-utils');
const path = require('path');

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'clean';
  
  console.log('Midscene 缓存清理工具');
  console.log('===================');
  
  const cacheDir = 'midscene_run/cache';
  const dumpDir = 'midscene_run/dump';
  const reportDir = 'midscene_run/report';
  
  switch (command) {
    case 'clean':
      console.log('清理缓存文件名...');
      cleanDirectory(cacheDir);
      cleanDirectory(dumpDir);
      cleanDirectory(reportDir);
      break;
      
    case 'remove':
      console.log('删除所有缓存...');
      removeDirectory('midscene_run');
      break;
      
    case 'cache':
      console.log('仅清理缓存目录...');
      cleanDirectory(cacheDir);
      break;
      
    default:
      console.log('用法:');
      console.log('  node scripts/clean-cache.js [command]');
      console.log('');
      console.log('命令:');
      console.log('  clean   - 清理文件名中的特殊字符 (默认)');
      console.log('  remove  - 删除整个 midscene_run 目录');
      console.log('  cache   - 仅清理缓存目录');
      break;
  }
  
  console.log('清理完成');
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  sanitizeFileName,
  cleanDirectory,
  removeDirectory
};
