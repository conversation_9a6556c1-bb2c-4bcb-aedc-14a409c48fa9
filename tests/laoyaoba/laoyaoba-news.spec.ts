import { test } from '../fixture/fixture';
import { LaoyaobaHomePage, NewsPage } from '../pages';
import { TestHelpers } from '../utils/test-helpers';

test.beforeEach(async ({ page }) => {
  await page.goto('https://www.laoyaoba.com/');
});

test.describe('laoyaoba-news', () => {

  test('get-latest-news', async ({ page, ai, aiQuery, assert }) => {
    // 创建页面对象
    const homePage = new LaoyaobaHomePage(page, assert, ai, aiQuery);
    const newsPage = new NewsPage(page, assert, ai, aiQuery);

    // 导航到最新新闻页面
    await homePage.navigateToLatestNews();

    // 验证新闻列表已加载
    await newsPage.verifyNewsListLoaded();

    // 获取最新新闻数据
    const { time, source } = await newsPage.getLatestNewsData();

    // 验证数据有效性
    assert.isTruthy(TestHelpers.isValidTimeFormat(time), '时间格式应该有效');
    assert.isTruthy(TestHelpers.isValidSourceFormat(source), '来源格式应该有效');
  });
});
