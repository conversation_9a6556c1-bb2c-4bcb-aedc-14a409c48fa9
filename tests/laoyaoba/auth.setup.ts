import { test as setup } from '../fixture/fixture';
import { LaoyaobaHomePage } from '../pages';
import { TestData } from '../data';

// setup('authenticate', async ({ page, ai, aiQuery, assert }) => {
//   // 创建首页页面对象
//   const homePage = new LaoyaobaHomePage(page, assert, ai, aiQuery);

//   // 访问首页
//   await homePage.visit();

//   // 执行登录操作
//   await homePage.login();

//   // 保存认证状态到文件
//   await homePage.saveAuthState(TestData.auth.authFile);
// });
