import { test as base } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { Assert } from '../utils/assertions';
import { MidsceneConfig } from '../utils/midscene-config';
import { WindowsCompatibility } from '../utils/windows-compatibility';
import { TestHelpers } from '../utils/test-helpers';

// 导入并应用 Midscene 补丁
import '../utils/midscene-patch';

// 在应用补丁后导入 PlaywrightAiFixture
import { PlaywrightAiFixture } from '@midscene/web/playwright';

// Define a new type that includes our custom assertion fixture
type MyFixtures = PlayWrightAiFixtureType & {
  assert: Assert;
};

// Extend the base test with both AI fixtures and our custom assertion fixture
export const test = base.extend<MyFixtures>({
  // Inherit the AI fixtures (已经通过补丁修改)
  ...PlaywrightAiFixture(),

  // Add our custom assert fixture
  assert: async ({ page }, use) => {
    await use(new Assert(page));
  },
});

// 在测试开始前清理可能存在问题的缓存文件
test.beforeAll(async () => {
  await WindowsCompatibility.cleanupProblematicCacheFiles();
});