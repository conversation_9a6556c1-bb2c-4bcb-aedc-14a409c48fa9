import { Page } from '@playwright/test';
import { TestData } from '../data/test-data';

/**
 * 测试辅助工具类
 * 包含通用的测试辅助方法
 */
export class TestHelpers {
  /**
   * 等待指定时间
   * @param ms - 等待时间（毫秒）
   */
  static async wait(ms: number): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 生成随机字符串
   * @param length - 字符串长度
   * @returns 随机字符串
   */
  static generateRandomString(length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 格式化当前时间戳
   * @returns 格式化的时间字符串
   */
  static getCurrentTimestamp(): string {
    return new Date().toISOString().replace(/[:.]/g, '-');
  }

  /**
   * 验证字符串是否为有效的时间格式
   * @param timeString - 时间字符串
   * @returns 是否为有效时间
   */
  static isValidTimeFormat(timeString: string): boolean {
    // 简单的时间格式验证，可以根据实际需求调整
    const timeRegex = /\d{4}-\d{2}-\d{2}|\d{2}:\d{2}|\d+分钟前|\d+小时前|\d+天前/;
    return timeRegex.test(timeString);
  }

  /**
   * 验证字符串是否为有效的来源格式
   * @param sourceString - 来源字符串
   * @returns 是否为有效来源
   */
  static isValidSourceFormat(sourceString: string): boolean {
    // 验证来源不为空且长度合理
    return !!sourceString && sourceString.trim().length > 0 && sourceString.length < 100;
  }

  /**
   * 清理字符串（去除多余空格和特殊字符）
   * @param str - 输入字符串
   * @returns 清理后的字符串
   */
  static cleanString(str: string): string {
    return str.trim().replace(/\s+/g, ' ');
  }

  /**
   * 将字符串转换为安全的文件名
   * @param str - 输入字符串
   * @returns 安全的文件名字符串
   */
  static sanitizeFileName(str: string): string {
    // Windows不支持的文件名字符：< > : " / \ | ? *
    // 同时替换中文括号和其他可能有问题的字符
    return str
      .replace(/[<>:"/\\|?*]/g, '-')  // 替换Windows不支持的字符
      .replace(/[（）]/g, '-')        // 替换中文括号
      .replace(/\s+/g, '-')          // 替换空格为连字符
      .replace(/-+/g, '-')           // 合并多个连字符
      .replace(/^-|-$/g, '');        // 去除开头和结尾的连字符
  }

  /**
   * 为Midscene生成安全的测试标识符
   * 将测试路径转换为Windows兼容的文件名
   * @param testPath - 测试路径，如 "laoyaoba-news.spec.ts > test-name"
   * @returns 安全的文件名
   */
  static createSafeTestId(testPath: string): string {
    return this.sanitizeFileName(testPath)
      .replace(/>/g, '_')           // 特别处理 > 符号，替换为下划线
      .replace(/\./g, '_')          // 替换点号为下划线
      .toLowerCase();               // 转换为小写
  }

  /**
   * 重试执行函数
   * @param fn - 要执行的函数
   * @param maxRetries - 最大重试次数
   * @param delay - 重试间隔（毫秒）
   * @returns 函数执行结果
   */
  static async retry<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        if (i < maxRetries) {
          await this.wait(delay);
        }
      }
    }
    
    throw lastError!;
  }
}
