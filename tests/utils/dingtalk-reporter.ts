import { Reporter } from '@playwright/test/reporter';
import type { TestCase, TestResult } from '@playwright/test/reporter';
import axios from 'axios';
import crypto from 'crypto';

interface FailedTest {
  title: string;
  error: string;
}

class DingTalkReporter implements Reporter {
  private webhookUrl: string;
  private secret?: string;
  private failedTests: FailedTest[] = [];

  constructor(options: { webhookUrl: string; secret?: string }) {
    this.webhookUrl = options.webhookUrl;
    this.secret = options.secret;
    // console.log('DingTalk Reporter initialized with webhook:', this.webhookUrl ? 'URL configured' : 'NO URL');
    // console.log('DingTalk Reporter secret:', this.secret ? 'Secret configured' : 'No secret');
  }

  onTestEnd(test: TestCase, result: TestResult) {
    // console.log(`Test ended: ${test.title} - Status: ${result.status}`);
    const safeTitle = test.title.replace(/[<>:"/\\|?*]/g, '-');
    if (result.status === 'failed') {
      let errorMessage = 'Unknown error';
      if (result.error) {
        // Format the error message to be more readable
        errorMessage = result.error.message || JSON.stringify(result.error);
        // Remove any ANSI color codes from the error message
        errorMessage = errorMessage.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, '');
      }
      this.failedTests.push({
        title: test.title,
        error: errorMessage
      });
      console.log(`Added failed test: ${test.title}`);
    }
  }

  private generateSignature(timestamp: number): { timestamp: number; sign: string } {
    if (!this.secret) {
      return { timestamp, sign: '' };
    }

    const stringToSign = `${timestamp}\n${this.secret}`;
    const sign = crypto
      .createHmac('sha256', this.secret)
      .update(stringToSign)
      .digest('base64');

    return { timestamp, sign };
  }

  async onEnd() {
    // console.log(`DingTalk Reporter onEnd called. Failed tests count: ${this.failedTests.length}`);
    if (this.failedTests.length === 0) {
      console.log('No failed tests, skipping DingTalk notification');
      return;
    }

    const failedTestsText = this.failedTests
      .map((test, index) => 
        `### ${index + 1}. ${test.title}\n` +
        `**错误信息**: \n\`\`\`\n${test.error}\n\`\`\`\n` +
        `---`
      )
      .join('\n\n');

    const message = {
      msgtype: 'markdown',
      markdown: {
        title: 'UI自动化测试失败',
        text: `### 🔴 UI自动化测试执行失败\n\n` +
          `**失败用例数**: ${this.failedTests.length}\n\n` +
          `**失败详情**:\n\n` +
          failedTestsText + '\n\n' +
          `**执行时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n\n` +
          `**关键词**: UI自动化`
      },
      at: {
        isAtAll: true
      }
    };

    try {
      // 生成签名（如果配置了密钥）
      const timestamp = Date.now();
      const { sign } = this.generateSignature(timestamp);

      // 构建最终的 webhook URL
      let finalWebhookUrl = this.webhookUrl;
      if (this.secret && sign) {
        const separator = this.webhookUrl.includes('?') ? '&' : '?';
        finalWebhookUrl = `${this.webhookUrl}${separator}timestamp=${timestamp}&sign=${encodeURIComponent(sign)}`;
      }

      // console.log('Sending DingTalk notification to:', finalWebhookUrl);
      // console.log('Message payload:', JSON.stringify(message, null, 2));

      const response = await axios.post(finalWebhookUrl, message, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // console.log('DingTalk notification sent successfully:', response.status, response.data);
    } catch (error) {
      console.error('Failed to send DingTalk notification:', error);
      if (axios.isAxiosError(error)) {
        // console.error('Response data:', error.response?.data);
        // console.error('Response status:', error.response?.status);
      }
    }
  }
}

export default DingTalkReporter;
