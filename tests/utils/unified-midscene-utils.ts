/**
 * Midscene 及 Windows 兼容性与通用工具合集
 * 合并自 midscene-config.ts, midscene-patch.ts, windows-compatibility.ts, test-helpers.ts 部分内容
 */

import { TestHelpers } from './test-helpers';

/**
 * Midscene 配置与兼容性工具
 */
export class MidsceneUtils {
  // 获取安全的缓存目录路径
  static getCacheDir(): string {
    return process.env.MIDSCENE_CACHE_DIR || 'midscene_run/cache';
  }

  // 是否启用安全文件名模式
  static isSafeFileNamesEnabled(): boolean {
    return process.env.MIDSCENE_SAFE_FILE_NAMES === 'true';
  }

  // 生成安全的文件名
  static generateSafeFileName(originalName: string): string {
    if (!this.isSafeFileNamesEnabled()) {
      return originalName;
    }
    return TestHelpers.sanitizeFileName(originalName)
      .replace(/>/g, '_')
      .replace(/\s+/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
  }

  // 创建兼容的测试ID
  static createCompatibleTestId(testTitle: string, specFile: string): string {
    const safeSpecFile = this.generateSafeFileName(specFile);
    const safeTestTitle = this.generateSafeFileName(testTitle);
    return `${safeSpecFile}_${safeTestTitle}`;
  }

  // 判断当前是否为 Windows
  static isWindows(): boolean {
    return process.platform === 'win32';
  }

  // 处理文件路径，确保 Windows 兼容
  static sanitizeFilePath(filePath: string): string {
    if (!this.isWindows()) {
      return filePath;
    }
    const parts = filePath.split(/[\\/]/);
    const sanitizedParts = parts.map(part => TestHelpers.sanitizeFileName(part));
    return sanitizedParts.join('\\');
  }

  // 自动设置 Windows 兼容性环境变量
  static setupWindowsCompatibility(): void {
    if (this.isWindows()) {
      process.env.MIDSCENE_SAFE_FILE_NAMES = 'true';
      if (process.env.MIDSCENE_CACHE_DIR) {
        process.env.MIDSCENE_CACHE_DIR = process.env.MIDSCENE_CACHE_DIR.replace(/\//g, '\\');
      }
    }
  }

  // 应用 Midscene Windows 补丁（fs monkey-patch）
  static applyMidsceneWindowsPatch() {
    if (!this.isWindows()) return;
    try {
      const fs = require('fs');
      const path = require('path');
      const originalWriteFileSync = fs.writeFileSync;
      const originalReadFileSync = fs.readFileSync;
      const originalExistsSync = fs.existsSync;
      const sanitizeFilePath = (filePath: string) => {
        if (typeof filePath !== 'string') return filePath;
        if (filePath.includes('midscene_run') && filePath.includes('cache')) {
          const dir = path.dirname(filePath);
          const fileName = path.basename(filePath);
          const safeFileName = TestHelpers.sanitizeFileName(fileName);
          return path.join(dir, safeFileName);
        }
        return filePath;
      };
      fs.writeFileSync = function(file: any, data: any, options?: any) {
        const safeFile = sanitizeFilePath(file);
        return originalWriteFileSync.call(this, safeFile, data, options);
      };
      fs.readFileSync = function(path: any, options?: any) {
        const safePath = sanitizeFilePath(path);
        return originalReadFileSync.call(this, safePath, options);
      };
      fs.existsSync = function(path: any) {
        const safePath = sanitizeFilePath(path);
        return originalExistsSync.call(this, safePath);
      };
      console.log('Midscene Windows 兼容性补丁已应用 (fs operations)');
    } catch (error) {
      console.warn('应用 Midscene 补丁时出错:', error);
    }
  }
}

// 自动设置兼容性和补丁
MidsceneUtils.setupWindowsCompatibility();
if (MidsceneUtils.isWindows()) {
  MidsceneUtils.applyMidsceneWindowsPatch();
}

// 导出默认配置对象
export const midsceneConfig = {
  cacheDir: MidsceneUtils.getCacheDir(),
  safeFileNames: MidsceneUtils.isSafeFileNamesEnabled(),
  fileNameGenerator: MidsceneUtils.generateSafeFileName,
  testIdGenerator: MidsceneUtils.createCompatibleTestId
};
