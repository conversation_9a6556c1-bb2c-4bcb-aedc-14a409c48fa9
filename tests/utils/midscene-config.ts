/**
 * Midscene 配置管理
 */

export class MidsceneConfig {
  // 获取安全的缓存目录路径
  static getCacheDir(): string {
    return process.env.MIDSCENE_CACHE_DIR || 'midscene_run/cache';
  }

  // 是否启用安全文件名模式
  static isSafeFileNamesEnabled(): boolean {
    return process.env.MIDSCENE_SAFE_FILE_NAMES === 'true';
  }

  // 自动设置 Windows 兼容性环境变量
  static setupWindowsCompatibility(): void {
    if (process.platform === 'win32') {
      process.env.MIDSCENE_SAFE_FILE_NAMES = 'true';
      if (process.env.MIDSCENE_CACHE_DIR) {
        process.env.MIDSCENE_CACHE_DIR = process.env.MIDSCENE_CACHE_DIR.replace(/\//g, '\\');
      }
    }
  }
}

// 自动设置兼容性
MidsceneConfig.setupWindowsCompatibility();

// 导出默认配置
export const midsceneConfig = {
  cacheDir: MidsceneConfig.getCacheDir(),
  safeFileNames: MidsceneConfig.isSafeFileNamesEnabled(),
};