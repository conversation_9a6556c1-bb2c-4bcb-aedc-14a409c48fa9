import { Page } from '@playwright/test';
import { Assert } from '../utils/assertions';
import { URLs } from '../data/urls';
import { TestData } from '../data/test-data';

/**
 * 基础页面类
 * 包含所有页面共用的方法和属性
 */
export abstract class BasePage {
  protected page: Page;
  protected assert: Assert;
  protected ai: any;
  protected aiQuery: any;

  constructor(page: Page, assert: Assert, ai: any, aiQuery: any) {
    this.page = page;
    this.assert = assert;
    this.ai = ai;
    this.aiQuery = aiQuery;
  }

  /**
   * 导航到指定 URL
   * @param url - 目标 URL
   */
  async navigateTo(url: string): Promise<void> {
    await this.page.goto(url);
  }

  /**
   * 导航到首页
   */
  async navigateToHome(): Promise<void> {
    await this.navigateTo(URLs.base);
  }

  /**
   * 等待页面加载完成
   * @param timeout - 超时时间，默认使用配置中的值
   */
  async waitForPageLoad(timeout: number = TestData.timeouts.networkIdle): Promise<void> {
    await this.page.waitForLoadState('networkidle', { timeout });
  }

  /**
   * 等待 URL 包含指定内容
   * @param urlFragment - URL 片段
   * @param errorMessage - 错误消息
   */
  async waitForUrlContains(urlFragment: string, errorMessage?: string): Promise<void> {
    await this.assert.urlToContain(urlFragment, errorMessage || TestData.messages.errors.urlNavigation);
  }

  /**
   * 执行单条 AI 操作
   * @param command - AI 指令
   */
  async performAiAction(command: string): Promise<void> {
    await this.ai(command);
  }

  /**
   * 批量执行 AI 操作
   * @param commands - AI 指令数组
   */
  async performAiActions(commands: string[]): Promise<void> {
    for (const cmd of commands) {
      await this.performAiAction(cmd);
    }
  }

  /**
   * 通用 AI 导航方法
   * @param aiCommand - AI 导航指令
   * @param urlFragment - 期望 URL 片段
   * @param errorMessage - 错误信息
   */
  async navigateByAiAction(aiCommand: string, urlFragment: string, errorMessage?: string): Promise<void> {
    await this.performAiAction(aiCommand);
    await this.waitForUrlContains(urlFragment, errorMessage);
  }

  /**
   * 执行 AI 查询
   * @param query - 查询指令
   * @returns 查询结果
   */
  async performAiQuery(query: string): Promise<string> {
    return await this.aiQuery(query);
  }

  /**
   * 获取当前页面 URL
   * @returns 当前页面 URL
   */
  getCurrentUrl(): string {
    return this.page.url();
  }

  /**
   * 验证页面标题
   * @param expectedTitle - 期望的标题
   */
  async verifyPageTitle(expectedTitle: string): Promise<void> {
    const title = await this.page.title();
    this.assert.isTruthy(title.includes(expectedTitle), `页面标题应包含: ${expectedTitle}`);
  }

  /**
   * 截图保存
   * @param filename - 文件名
   */
  async takeScreenshot(filename: string): Promise<void> {
    await this.page.screenshot({ path: `test-results/${filename}` });
  }
}
