import { Page } from '@playwright/test';
import { BasePage } from './base-page';
import { Assert } from '../utils/assertions';
import { URLs } from '../data/urls';
import { TestData } from '../data/test-data';

/**
 * 老鸭窝首页页面对象
 */
export class LaoyaobaHomePage extends BasePage {
  constructor(page: Page, assert: Assert, ai: any, aiQuery: any) {
    super(page, assert, ai, aiQuery);
  }

  /**
   * 访问首页
   */
  async visit(): Promise<void> {
    await this.navigateToHome();
  }

  /**
   * 执行登录操作
   */
  async login(): Promise<void> {
    // 批量执行登录相关AI操作
    await this.performAiActions([
      TestData.aiCommands.navigation.clickLogin,
      TestData.aiCommands.navigation.clickAccountLogin,
      TestData.aiCommands.input.enterPhone,
      TestData.aiCommands.input.enterPassword,
      TestData.aiCommands.navigation.clickLoginButton
    ]);
    // 等待登录成功，返回首页
    await this.page.waitForURL(URLs.base, { timeout: TestData.timeouts.login });
  }

  /**
   * 导航到舆情页面
   */
  async navigateToOpinion(): Promise<void> {
    await this.navigateByAiAction(
      TestData.aiCommands.navigation.clickOpinion,
      URLs.fragments.opinion,
      'URL should navigate to the opinion page'
    );
  }

  /**
   * 导航到最新新闻页面
   */
  async navigateToLatestNews(): Promise<void> {
    await this.navigateByAiAction(
      TestData.aiCommands.navigation.clickLatest,
      URLs.fragments.news,
      'URL should navigate to the news page'
    );
  }

  /**
   * 保存认证状态
   * @param authFilePath - 认证文件路径
   */
  async saveAuthState(authFilePath: string = TestData.auth.authFile): Promise<void> {
    await this.page.context().storageState({ path: authFilePath });
  }
}
