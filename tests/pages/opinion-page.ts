import { Page } from '@playwright/test';
import { BasePage } from './base-page';
import { Assert } from '../utils/assertions';
import { URLs } from '../data/urls';
import { TestData } from '../data/test-data';

/**
 * 舆情页面对象
 */
export class OpinionPage extends BasePage {
  constructor(page: Page, assert: Assert, ai: any, aiQuery: any) {
    super(page, assert, ai, aiQuery);
  }

  /**
   * 导航到行业热点
   */
  async navigateToHotNews(): Promise<void> {
    await this.performAiAction(TestData.aiCommands.navigation.clickHotNews);
    await this.waitForUrlContains(URLs.fragments.hotNews, 'URL should navigate to the hot news page');
  }

  /**
   * 点击时间排序
   */
  async sortByTime(): Promise<void> {
    await this.performAiAction(TestData.aiCommands.navigation.clickTimeSort);
    await this.waitForPageLoad();
  }

  /**
   * 获取第一条新闻的发布时间
   * @returns 发布时间
   */
  async getFirstNewsTime(): Promise<string> {
    const time = await this.performAiQuery(TestData.aiCommands.query.getFirstNewsTime);
    this.assert.isTruthy(time, TestData.messages.errors.timeEmpty);
    return time;
  }

  /**
   * 获取第一条新闻的来源
   * @returns 新闻来源
   */
  async getFirstNewsSource(): Promise<string> {
    const source = await this.performAiQuery(TestData.aiCommands.query.getFirstNewsSource);
    this.assert.isTruthy(source, TestData.messages.errors.sourceEmpty);
    return source;
  }

  /**
   * 获取最新舆情数据
   * @returns 包含时间和来源的对象
   */
  async getLatestOpinionData(): Promise<{ time: string; source: string }> {
    // 先按时间排序
    await this.sortByTime();
    
    // 获取第一条数据的时间和来源
    const time = await this.getFirstNewsTime();
    const source = await this.getFirstNewsSource();

    console.log(`最新一条数据的发布时间: ${time}`);
    console.log(`最新一条数据的来源: ${source}`);

    return { time, source };
  }
}
