import { Page } from '@playwright/test';
import { BasePage } from './base-page';
import { Assert } from '../utils/assertions';
import { TestData } from '../data/test-data';

/**
 * 新闻页面对象
 */
export class NewsPage extends BasePage {
  constructor(page: Page, assert: Assert, ai: any, aiQuery: any) {
    super(page, assert, ai, aiQuery);
  }

  /**
   * 获取第一条新闻的发布时间
   * @returns 发布时间
   */
  async getFirstNewsTime(): Promise<string> {
    const time = await this.performAiQuery(TestData.aiCommands.query.getFirstNewsTime);
    this.assert.isTruthy(time, TestData.messages.errors.timeEmpty);
    return time;
  }

  /**
   * 获取第一条新闻的来源
   * @returns 新闻来源
   */
  async getFirstNewsSource(): Promise<string> {
    const source = await this.performAiQuery(TestData.aiCommands.query.getFirstNewsSource);
    this.assert.isTruthy(source, TestData.messages.errors.sourceEmpty);
    return source;
  }

  /**
   * 获取最新新闻数据
   * @returns 包含时间和来源的对象
   */
  async getLatestNewsData(): Promise<{ time: string; source: string }> {
    // 获取第一条新闻的时间和来源
    const time = await this.getFirstNewsTime();
    const source = await this.getFirstNewsSource();

    console.log(`最新一条数据的发布时间: ${time}`);
    console.log(`最新一条数据的来源: ${source}`);

    return { time, source };
  }

  /**
   * 验证新闻列表是否加载
   */
  async verifyNewsListLoaded(): Promise<void> {
    // 可以添加具体的验证逻辑
    // 例如检查是否有新闻项目存在
    await this.waitForPageLoad();
  }
}
