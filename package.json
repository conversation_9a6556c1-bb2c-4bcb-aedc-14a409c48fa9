{"name": "playwright-mind", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:windows": "node scripts/run-tests-windows.js", "test:laoyaoba": "node scripts/run-tests-windows.js tests/laoyaoba/laoyaoba-news.spec.ts", "clean:cache": "node scripts/clean-cache.js clean", "clean:cache:remove": "node scripts/clean-cache.js remove", "clean:cache:only": "node scripts/clean-cache.js cache", "pretest": "node scripts/clean-cache.js clean"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@midscene/web": "^0.9.2", "@playwright/test": "^1.50.1", "@types/node": "^22.16.5", "axios": "^1.9.0"}, "dependencies": {"@executeautomation/playwright-mcp-server": "^0.2.7", "dotenv": "^16.4.7", "playwright-mind": "file:"}}